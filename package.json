{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"lint": "eslint .", "prettify": "npx prettier --write .", "contracts": "scripts/deployContract.sh", "graph": "scripts/deploySubgraph.sh", "wagmi": "wagmi generate", "dev": "bun run wagmi && vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@privy-io/react-auth": "^2.17.3", "@privy-io/wagmi": "^1.0.5", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@wagmi/cli": "^2.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "lucide-react": "^0.525.0", "permissionless": "^0.2.50", "qr-code-styling": "^1.9.2", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "wagmi": "^2.15.6", "wouter": "^3.7.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "shadcn": "^2.7.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "vite": "^7.0.2"}}