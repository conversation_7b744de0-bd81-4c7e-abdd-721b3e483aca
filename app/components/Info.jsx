import { formatUnits } from 'viem'
import { useAccount } from '../wallet'

import { useReadErc20BalanceOf } from '../contracts';


export default function Info() {
    const { address } = useAccount();
    const { data: balance, isSuccess } = useReadErc20BalanceOf({ args: [address] });

    return (
        <div>
            <p>{address}</p>
            { isSuccess ?  <p>${formatUnits(balance, 6)}</p> : <p>Loading...</p> }
        </div>
    )
}