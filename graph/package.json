{"name": "laf-dev", "license": "UNLICENSED", "scripts": {"codegen": "graph codegen", "build": "graph build", "deploy": "graph deploy --node https://api.studio.thegraph.com/deploy/ laf-dev", "create-local": "graph create --node http://localhost:8020/ laf-dev", "remove-local": "graph remove --node http://localhost:8020/ laf-dev", "deploy-local": "graph deploy --node http://localhost:8020/ --ipfs http://localhost:5001 laf-dev", "test": "graph test"}, "dependencies": {"@graphprotocol/graph-cli": "0.97.1", "@graphprotocol/graph-ts": "^0.38.1"}, "devDependencies": {"matchstick-as": "0.6.0"}}