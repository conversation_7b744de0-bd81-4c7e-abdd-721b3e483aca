type ApprovalForAll @entity(immutable: true) {
  id: Bytes!
  account: Bytes! # address
  operator: Bytes! # address
  approved: Boolean! # bool
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type Down @entity(immutable: true) {
  id: Bytes!
  from: Bytes! # address
  to: Bytes! # address
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type ItemFound @entity(immutable: true) {
  id: Bytes!
  item: Bytes! # address
  hash: Bytes! # address
  owner: Bytes! # original owner of the item
  finder: Bytes! # address of who found the item
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type ItemLost @entity(immutable: true) {
  id: Bytes!
  item: Bytes! # address
  hash: Bytes! # address
  owner: Bytes! # address
  rewardAmount: BigInt! # reward amount
  geoLocation: String! # geographic location
  latitude: BigDecimal # parsed latitude
  longitude: BigDecimal # parsed longitude
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type ItemRegistered @entity(immutable: true) {
  id: Bytes!
  item: Bytes! # address
  hash: Bytes! # address
  owner: Bytes! # address
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type ItemReturned @entity(immutable: true) {
  id: Bytes!
  item: Bytes! # address
  hash: Bytes! # address
  owner: Bytes! # address
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

# Enhanced entity to track current item status efficiently
type ItemStatus @entity(immutable: false) {
  id: Bytes! # item contract address
  item: Bytes! # item contract address
  owner: Bytes! # original owner
  finder: Bytes # who found the item (null if not found)
  hash: Bytes! # secret hash
  status: String! # current status
  
  # Geographic data (from ItemLost event)
  geoLocation: String # geographic location
  latitude: BigDecimal # parsed latitude
  longitude: BigDecimal # parsed longitude
  
  # Timestamps
  registeredAt: BigInt!
  lostAt: BigInt
  foundAt: BigInt
  returnedAt: BigInt
  lastUpdated: BigInt!
  
  # Event references
  registeredEvent: Bytes!
  lostEvent: Bytes
  foundEvent: Bytes
  returnedEvent: Bytes
  
  # Transaction hashes
  registeredTx: Bytes!
  lostTx: Bytes
  foundTx: Bytes
  returnedTx: Bytes
}

type OwnershipTransferred @entity(immutable: true) {
  id: Bytes!
  previousOwner: Bytes! # address
  newOwner: Bytes! # address
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type TransferBatch @entity(immutable: true) {
  id: Bytes!
  operator: Bytes! # address
  from: Bytes! # address
  to: Bytes! # address
  ids: [BigInt!]! # uint256[]
  values: [BigInt!]! # uint256[]
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type TransferSingle @entity(immutable: true) {
  id: Bytes!
  operator: Bytes! # address
  from: Bytes! # address
  to: Bytes! # address
  internal_id: BigInt! # uint256
  value: BigInt! # uint256
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type URI @entity(immutable: true) {
  id: Bytes!
  value: String! # string
  internal_id: BigInt! # uint256
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}

type Up @entity(immutable: true) {
  id: Bytes!
  from: Bytes! # address
  to: Bytes! # address
  blockNumber: BigInt!
  blockTimestamp: BigInt!
  transactionHash: Bytes!
}
