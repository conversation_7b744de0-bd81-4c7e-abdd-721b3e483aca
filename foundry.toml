[profile.default]
src = "contracts/src"
out = "contracts/out"
libs = ["contracts/lib"]
script = 'contracts/scripts'
broadcast = 'contracts/broadcast'
cache_path = 'contracts/cache'
test = 'contracts/test'

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options


remappings = [
    '@openzeppelin/=contracts/lib/openzeppelin-contracts/',
    'forge-std/=contracts/lib/forge-std/src/'
]